# 关机充电数码管显示电量功能测试说明

## 修改内容总结

### 1. 关机充电流程分析
- **入口**: `mmi.c` 中调用 `zMmi_PowerOffChargerEntry()`
- **状态获取**: `ofchg_get_charge_status()` 从 `/sys/class/power_supply/charger/status` 获取充电状态
- **状态消费**: `mmi_getLedPowerOffChagerInfo()` 根据充电状态控制LED和数码管显示

### 2. 修改的文件和函数
**文件**: `ap/app/zte_comm/zte_mmi/mmi_led.c`
**主要修改**:
1. 添加了函数声明（第14-22行）
2. 修改了 `mmi_getLedPowerOffChagerInfo()` 函数（第1005-1121行）

### 3. 新增功能
在 `JCV_CHARLIEPLEX_LED_DIGITS` 宏定义保护下，添加了以下功能：

#### 3.1 电量获取和计算
```c
// 获取电压值
voltagepower = offchg_voltage_state_read();

// 根据电压和充电状态计算电量百分比
battery_percent = get_charging_voltage_level_from_table(voltagepower, 
    (pPocInfo->poc_sta == POC_STATE_CHARGING || pPocInfo->poc_sta == POC_STATE_FULL));
```

#### 3.2 不同充电状态的显示逻辑
1. **充电状态** (`POC_STATE_CHARGING`):
   - 显示: 闪电图标(L1) + 百分号(L2) + 电量百分比
   - 命令: `echo L1ONL2ON{percent} > /dev/charlieplex_display`

2. **充满状态** (`POC_STATE_FULL`):
   - 显示: 百分号(L2) + 100%
   - 命令: `echo L1OFFL2ON100 > /dev/charlieplex_display`

3. **低电量状态** (`POC_STATE_LOWBATTERY`):
   - 显示: 仅电量百分比
   - 命令: `echo L1OFFL2OFF{percent} > /dev/charlieplex_display`

4. **其他状态**:
   - 显示: 仅电量百分比
   - 命令: `echo L1OFFL2OFF{percent} > /dev/charlieplex_display`

#### 3.3 优化特性
- **防重复刷新**: 只有当电量百分比或显示状态改变时才更新数码管
- **休眠支持**: 支持 `QRZL_DIGITS_SLEEP_WAKEUP_FEATURE` 功能
- **强制刷新**: 支持强制刷新数码管显示
- **日志记录**: 详细的调试日志输出

### 4. 添加的函数声明
```c
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
extern SINT32 offchg_voltage_state_read(VOID);
extern SINT32 get_charging_voltage_level_from_table(SINT32 voltagepower, BOOL charging_status);
#endif
```

### 5. 核心代码逻辑
修改后的 `mmi_getLedPowerOffChagerInfo` 函数主要逻辑：

```c
SINT32 mmi_getLedPowerOffChagerInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
    // 变量声明
    T_zMmi_Poc_Info *pPocInfo = (T_zMmi_Poc_Info *)taskInfo;
    T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;

#ifdef JCV_CHARLIEPLEX_LED_DIGITS
    static SINT32 s_last_poweroff_bat_pers = -1;
    static BOOL s_last_poweroff_L1_was_on = FALSE;
    static BOOL s_is_poweroff_first_call = TRUE;
    char buf[64] = {0};
    SINT32 voltagepower = 0;
    SINT32 battery_percent = 0;

    // 获取电压和计算电量百分比
    voltagepower = offchg_voltage_state_read();
    if (voltagepower > 0) {
        battery_percent = get_charging_voltage_level_from_table(voltagepower,
            (pPocInfo->poc_sta == POC_STATE_CHARGING || pPocInfo->poc_sta == POC_STATE_FULL));

        // 根据不同充电状态显示
        if (pPocInfo->poc_sta == POC_STATE_CHARGING) {
            // 充电：L1(闪电) + L2(%) + 数字
            snprintf(buf, sizeof(buf), "echo L1ONL2ON%d > /dev/charlieplex_display", battery_percent);
        } else if (pPocInfo->poc_sta == POC_STATE_FULL) {
            // 充满：L2(%) + 100
            snprintf(buf, sizeof(buf), "echo L1OFFL2ON100 > /dev/charlieplex_display");
        } else {
            // 其他：仅显示数字
            snprintf(buf, sizeof(buf), "echo L1OFFL2OFF%d > /dev/charlieplex_display", battery_percent);
        }
        system(buf);
    }
#endif

    // 原有LED控制逻辑保持不变
    // ...
}
```

## 测试方法

### 1. 编译测试
确保在启用 `JCV_CHARLIEPLEX_LED_DIGITS` 宏的情况下编译通过。

### 2. 功能测试
1. **进入关机充电模式**
2. **连接充电器**，观察数码管显示：
   - 应显示闪电图标 + 百分号 + 当前电量百分比
3. **充电至满电**，观察数码管显示：
   - 应显示百分号 + 100%
4. **断开充电器**（低电量状态），观察数码管显示：
   - 应仅显示电量百分比

### 3. 日志验证
通过串口或日志查看相关调试信息：
```
ZTE_MMI poweroff charger: voltage=xxxx, percent=xx, poc_sta=x
ZTE_MMI poweroff charger: charging display xx%
ZTE_MMI poweroff charger: full display 100%
ZTE_MMI poweroff charger: low battery display xx%
```

## 注意事项

1. **宏定义依赖**: 功能仅在定义了 `JCV_CHARLIEPLEX_LED_DIGITS` 时生效
2. **硬件依赖**: 需要硬件支持数码管显示功能
3. **驱动依赖**: 需要 `/dev/charlieplex_display` 设备节点存在
4. **电量计算**: 依赖 `get_charging_voltage_level_from_table` 函数的准确性

## 相关文件
- `ap/app/zte_comm/zte_mmi/mmi_led.c` - 主要修改文件
- `ap/app/zte_comm/zte_mmi/mmi_poweroff_charger.c` - 电压读取函数
- `ap/app/zte_comm/zte_mmi/mmi_battery_adapter.c` - 电量计算函数
- `ap/os/linux/linux-3.4.x/drivers/leds/charlieplex_led_digits.c` - 数码管驱动
