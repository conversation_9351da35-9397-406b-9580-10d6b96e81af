/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_led.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#include "mmi_common.h"

/********************************************************************************
  函数声明
**********************************************************************************/
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
// 声明关机充电时需要用到的函数
extern SINT32 offchg_voltage_state_read(VOID);
extern SINT32 get_charging_voltage_level_from_table(SINT32 voltagepower, BOOL charging_status);
#endif

/********************************************************************************
  ȫ�ֱ�������
**********************************************************************************/
extern UINT32 g_smstask_enable;
extern UINT32 g_voicetask_enable;
extern UINT32 g_led_sleep_mode;

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
/* 数码管休眠状态管理变量 */
static BOOL s_digits_sleeping = FALSE;  /* 数码管是否处于休眠状态 */
BOOL s_digits_timer_running = FALSE;  /* 数码管休眠定时器是否正在运行 */
static BOOL s_digits_force_refresh = FALSE;  /* 是否需要强制刷新数码管显示 */
#endif

/*****************************************************************************
 ȫ�ֱ�������
******************************************************************************/
MMI_LED_STATE s_mmi_batled_laststate = LED_STATE_MAX;//��ǵ�صƵ���һ��״̬
MMI_LED_STATE s_mmi_wanled_laststate = LED_STATE_MAX;//�������Ƶ���һ��״̬
MMI_LED_STATE s_mmi_lanled_laststate = LED_STATE_MAX;//���wifi�Ƶ���һ��״̬
MMI_LED_STATE s_mmi_smsled_laststate = LED_STATE_MAX;//��Ƕ��ŵƵ���һ��״̬
MMI_LED_STATE s_mmi_traled_laststate = LED_STATE_MAX;//��������Ƶ���һ��״̬
MMI_LED_STATE s_mmi_voiled_laststate = LED_STATE_MAX;//��������Ƶ���һ��״̬

extern POWER_ON_OFF_CALLBACK_FUN g_PowerOnOffFun;
extern T_zMmiSmsLedConfig g_mmi_smsled_config_tab[];
extern T_zMmiWifiLedConfig g_mmi_wifiled_config_tab[];
extern T_zMmiNetLedConfig g_mmi_netled_config_tab[];
extern T_zMmiBatteryLedConfig g_mmi_batled_config_tab[];
extern T_zMmiVoipLedConfig g_mmi_voipled_config_tab[];
extern T_zMmiNetLedConfig g_mmi_signalled_config_tab[];
extern T_zMmiWifiLedConfig g_mmi_wpsled_config_tab[];
extern T_zMmiRj11LedConfig g_mmi_rj11led_config_tab[];


extern sem_t g_mmi_traffic_warn_sem_id;
BOOL s_mmi_trafficled_warn_flag = FALSE;//��������澯���Ƿ�����˫����
extern int g_customer_type;
/**********************************************************************************
*��������:LED�Ʋ���
***********************************************************************************/
static VOID mmi_led_operate_blink_off(T_zMmi_Led_Info*  userInfo)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_led_operate name= %d\n", userInfo->led_name);
	T_zMmi_Led_Info tmpInfo = {0};
	memcpy((VOID *)(&tmpInfo), (const VOID *)userInfo, sizeof(T_zMmi_Led_Info));
	tmpInfo.led_state = LED_STATE_OFF;
	tmpInfo.led_color = userInfo->led_color;
	mmi_led_operate(&tmpInfo);

}

static VOID mmi_led_operate_set_blinktime(T_zMmi_Led_Blink_Time *blink_time, MMI_LED_BLINK_SPEED speed)
{
	switch (speed) {
	case LED_STATE_FAST_BLINK: {
		blink_time->uBlinkOnTime = LED_FAST_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_FAST_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_SLOW_BLINK: {
		blink_time->uBlinkOnTime = LED_SLOW_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_SLOW_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_BAT_BLINK: {
		blink_time->uBlinkOnTime = LED_BAT_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_BAT_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_LAN_BLINK: {
		blink_time->uBlinkOnTime = LED_LAN_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_LAN_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_SMS_BLINK: {
		blink_time->uBlinkOnTime = LED_SMS_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_SMS_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_WAN_BLINK: {
		blink_time->uBlinkOnTime = LED_WAN_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_WAN_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_VOIP_BLINK: {
		blink_time->uBlinkOnTime = LED_VOIP_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_VOIP_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_WAN_FAST_BLINK: {
		blink_time->uBlinkOnTime = LED_WAN_FAST_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_WAN_FAST_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_WAN_SLOW_BLINK: {
		blink_time->uBlinkOnTime = LED_WAN_SLOW_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_WAN_SLOW_BLINK_OFF_TIME;
		break;
	}
	//yaoyuan cpe
	case LED_STATE_WAN_CPE_FAST_BLINK: {
		blink_time->uBlinkOnTime = LED_WAN_CPE_FAST_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_WAN_CPE_FAST_BLINK_OFF_TIME;
		break;
	}
	case LED_STATE_WAN_CPE_SLOW_BLINK: {
		blink_time->uBlinkOnTime = LED_WAN_CPE_SLOW_BLINK_ON_TIME;
		blink_time->uBlinkOffTime = LED_WAN_CPE_SLOW_BLINK_OFF_TIME;
		break;
	}
	default: {
		break;
	}
	}
}

static VOID mmi_set_led_laststate(T_zMmi_Led_Info*  userInfo)
{
	switch (userInfo->led_name) {
	case LED_BATTERY: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_batled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_batled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_batled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_batled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_WAN: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_wanled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_wanled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_wanled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_wanled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_LAN: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_lanled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_lanled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_lanled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_lanled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_SMS: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_smsled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_smsled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_smsled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_smsled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_TRAFFIC: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_traled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_traled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_traled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_traled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_VOIP: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_voiled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_voiled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_voiled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_voiled_laststate = LED_STATE_MAX;
		}
		break;
	}
#if 0
	//yao yuan
	case LED_SIGNAL: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_sigled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_voiled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_sigled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_sigled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_WPS: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_wpsled_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_voiled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_wpsled_laststate = LED_STATE_MAX;
		} else {
			s_mmi_wpsled_laststate = LED_STATE_MAX;
		}
		break;
	}
	case LED_RJ11: {
		if (userInfo->led_state == LED_STATE_BLINK) {
			s_mmi_rj11led_laststate = LED_STATE_BLINK;
		} else if (userInfo->led_state == LED_STATE_ON && s_mmi_voiled_laststate == LED_STATE_BLINK) {
			mmi_led_operate_blink_off(userInfo);
			s_mmi_rj11led_laststate = LED_STATE_MAX;
		} else {
			s_mmi_rj11led_laststate = LED_STATE_MAX;
		}
		break;
	}
#endif	
	default: {
		break;
	}
	}
}

SINT32 mmi_showLed(UINT32 ledInfo)
{
	T_zMmi_Led_Info *led_info = (T_zMmi_Led_Info *)ledInfo;
	T_zMmi_Led_Blink_Time blink_time = {0};
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_showLed name= %d ,state = %d, color=%d !\n", led_info->led_name, led_info->led_state, led_info->led_color);

	mmi_set_led_laststate(led_info);
	if (led_info->led_state == LED_STATE_BLINK) {
		mmi_led_operate_set_blinktime(&blink_time, led_info->ledBlink_speed);
		led_info->ledBlink_time = blink_time;
	}
	if (led_info->led_name == LED_TRAFFIC && s_mmi_traled_laststate == LED_STATE_BLINK && led_info->led_state == LED_STATE_OFF) {
		mmi_led_operate(led_info);//��������һ��״̬ʱ��˸����Ҫ�����β��ܹص�
	}
	mmi_led_operate(led_info);
	return 0;
}

static VOID mmi_setLedShowInfo(T_zMmi_Led_Info* outLedInfo, T_zMmi_Led_Info inLedInfo)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_setLedShowInfo inLedInfo name:%d,state:%d,color:%d\n", inLedInfo.led_name, inLedInfo.led_state, inLedInfo.led_color);
	outLedInfo->led_name = inLedInfo.led_name;
	outLedInfo->led_color = inLedInfo.led_color;
	outLedInfo->traffic = inLedInfo.traffic;
	outLedInfo->led_state = inLedInfo.led_state;
	outLedInfo->ledBlink_speed = inLedInfo.ledBlink_speed;

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_setLedShowInfo outLedInfo name:%d,state:%d,color:%d\n", outLedInfo->led_name, outLedInfo->led_state, outLedInfo->led_color);
}
/**********************************************************************************
*��������:��ȡ���ŵ���Ϣ
***********************************************************************************/
SINT32 mmi_getLedSmsInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMmi_Sms_Info *pSmsInfo = (T_zMmi_Sms_Info*)taskInfo;
	UINT32 i = 0;
	
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedSmsInfo recvBox_sta=%d\n",pSmsInfo->recvBox_sta);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_SMS) / sizeof(T_zMmiSmsLedConfig); ++ i) {
		if (pSmsInfo->recvBox_sta == g_mmi_smsled_config_tab[i].sms_info.recvBox_sta) {
			mmi_setLedShowInfo(pLedInfo, g_mmi_smsled_config_tab[i].led_info);
			return MMI_SUCCESS;
		}
	}
	return MMI_ERROR;
}
/**********************************************************************************
*��������:��ȡ������ʾ����Ϣ
***********************************************************************************/
SINT32 mmi_getLedVoipInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMmi_Voip_Info *pVoipInfo = (T_zMmi_Voip_Info*)taskInfo;
	UINT32 i = 0;
	
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedVoipInfo voip_sta=%d\n",pVoipInfo->voip_sta);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_VOIP) / sizeof(T_zMmiVoipLedConfig); ++ i) {
		if (pVoipInfo->voip_sta == g_mmi_voipled_config_tab[i].voip_info.voip_sta) {
			mmi_setLedShowInfo(pLedInfo, g_mmi_voipled_config_tab[i].led_info);
			return MMI_SUCCESS;
		}
	}
	return MMI_ERROR;
}
/**********************************************************************************
*��������:��ȡrj11����Ϣ ҢԶcpe
***********************************************************************************/
SINT32 mmi_getLedRj11Info(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMmi_Rj11_Info *pVoipInfo = (T_zMmi_Rj11_Info*)taskInfo;
	UINT32 i = 0;

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedRj11Info rj11_sta=%d\n",pVoipInfo->rj11_sta);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_RJ11) / sizeof(T_zMmiRj11LedConfig); ++ i) {
		if (pVoipInfo->rj11_sta == g_mmi_rj11led_config_tab[i].rj11_info.rj11_sta) {
			mmi_setLedShowInfo(pLedInfo, g_mmi_rj11led_config_tab[i].led_info);
			return MMI_SUCCESS;
		}
	}
	return MMI_ERROR;
}

/**********************************************************************************
*��������:��ȡ��ص���Ϣ
***********************************************************************************/
SINT32 mmi_getLedBatteryInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMMIBatteryInfo *pBatteryInfo = (T_zMMIBatteryInfo *)taskInfo;
	UINT32 i = 0;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedBatteryInfo chg_sta=%d\n", pBatteryInfo->chg_state);

#ifdef JCV_CHARLIEPLEX_LED_DIGITS
	char buf[64] = {0};
	static SINT32 s_last_bat_pers_for_display = 0;
	static BOOL s_last_L1_was_on = FALSE;
	static BOOL s_is_first_call = TRUE;

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
	/* 如果数码管处于休眠状态，则不更新显示 */
	if (s_digits_sleeping) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI digits is sleeping, skip display update\n");
		goto skip_digits_display;
	}
#endif

	//itoa((int)(pBatteryInfo->bat_pers), buf, 10);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI salvikie s_is_first_call=%d s_last_L1_was_on=%d s_last_bat_pers_for_display=%d pBatteryInfo->bat_level=%d bat_pers=%d bat_grid=%d\n",
									s_is_first_call, s_last_L1_was_on, s_last_bat_pers_for_display, pBatteryInfo->bat_level, pBatteryInfo->bat_pers, pBatteryInfo->bat_grid);

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
	/* 如果需要强制刷新，重置显示状态 */
	if (s_digits_force_refresh) {
		s_is_first_call = TRUE;
		s_last_bat_pers_for_display = -1;
		s_last_L1_was_on = FALSE;
		s_digits_force_refresh = FALSE;
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI force refresh digits display\n");
	}
#endif

	if (s_is_first_call) {
		if (pBatteryInfo->chg_state == STATE_CHARGING) {
			//充电状态 数码管显示闪电图标 
			// 这里为什么会导致死机
			//mmi_file_operate(JCV_CHARLIEPLEX_LED_DIGITS, "L1ON");
			// mmi_sleep(LED_BLINK_TIME_INTERVAL);
			// mmi_file_operate(JCV_CHARLIEPLEX_LED_DIGITS, "L2ON");
			// mmi_sleep(LED_BLINK_TIME_INTERVAL);
			// mmi_file_operate(JCV_CHARLIEPLEX_LED_DIGITS, buf);
			//system("echo 98 > /dev/charlieplex_display");
			snprintf(buf, sizeof(buf), "echo L1ONL2ON%d > /dev/charlieplex_display", pBatteryInfo->bat_pers);
			system(buf);
			s_last_bat_pers_for_display = pBatteryInfo->bat_pers;
			s_last_L1_was_on = TRUE;

		} else if (pBatteryInfo->chg_state == STATE_FULL) {
			snprintf(buf, sizeof(buf), "echo L1OFFL2ON100 > /dev/charlieplex_display");
			system(buf);
			s_last_bat_pers_for_display = 100;
			s_last_L1_was_on = FALSE;
		} else {
			snprintf(buf, sizeof(buf), "echo L1OFFL2ON%d > /dev/charlieplex_display", pBatteryInfo->bat_pers);
			system(buf);
			s_last_bat_pers_for_display = pBatteryInfo->bat_pers;
			s_last_L1_was_on = FALSE;
		}
		s_is_first_call = FALSE;
	} else {
		BOOL current_L1_should_be_on = (pBatteryInfo->chg_state == STATE_CHARGING);
		if (current_L1_should_be_on != s_last_L1_was_on) {
			if (current_L1_should_be_on) {
            	system("echo L1ON > /dev/charlieplex_display");
			} else {
				system("echo L1OFF > /dev/charlieplex_display");
			}
        	s_last_L1_was_on = current_L1_should_be_on;
		}

		if (pBatteryInfo->bat_pers != s_last_bat_pers_for_display) {
			snprintf(buf, sizeof(buf), "echo %d > /dev/charlieplex_display", pBatteryInfo->bat_pers);
			system(buf);
        	s_last_bat_pers_for_display = pBatteryInfo->bat_pers;
    	}
	}

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
skip_digits_display:
#endif
#endif

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_BATTERY) / sizeof(T_zMmiBatteryLedConfig); ++ i) {
		if (pBatteryInfo->chg_state == g_mmi_batled_config_tab[i].bat_info.chg_state) {
			if (pBatteryInfo->chg_state == STATE_DISCHARGE) {
				if (pBatteryInfo->bat_level == g_mmi_batled_config_tab[i].bat_info.bat_level) {
					mmi_setLedShowInfo(pLedInfo, g_mmi_batled_config_tab[i].led_info);
					return MMI_SUCCESS;
				}
			}
		#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
			else if (pBatteryInfo->chg_state == STATE_CHARGING) {
				if (pBatteryInfo->bat_level == g_mmi_batled_config_tab[i].bat_info.bat_level) {
					mmi_setLedShowInfo(pLedInfo, g_mmi_batled_config_tab[i].led_info);
					return MMI_SUCCESS;
				}
			}
		#endif 
			else {
				mmi_setLedShowInfo(pLedInfo, g_mmi_batled_config_tab[i].led_info);
				return MMI_SUCCESS;
			}
		}
	}
	return MMI_ERROR;
}

/**********************************************************************************
*��������:��ȡ�������Ϣ
***********************************************************************************/
SINT32 mmi_getLedNetInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMMINetInfo *pNetInfo = (T_zMMINetInfo *)taskInfo;
	UINT32 i = 0;
	int customer_type = g_customer_type; 
	E_zMmi_Net_Mode net_mode = pNetInfo->net_mode;
	char mmi_nv_value[32] = {0};
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo net_mode=%d connect %d socket %d customer_type=%d\n",pNetInfo->net_mode,pNetInfo->connect_status,pNetInfo->socket_state,g_customer_type);

	cfg_get_item("mmi_led_plan", mmi_nv_value, sizeof(mmi_nv_value));
	if (0 == strcmp(mmi_nv_value, "yaoyuan")) {
		customer_type = CUSTOMER_YAOYUAN;
		
		cfg_get_item(NV_MODEM_MAIN_STATE, mmi_nv_value, sizeof(mmi_nv_value));
		if (strcmp(mmi_nv_value, NV_SIM_STATE_INIT_COMPLETE)) {
			net_mode = NET_MODE_NOTREADY;
		}
	}

#if defined(JCV_HW_MZ803_V3_2) || defined(JCV_HW_UZ901_V1_4)
	char ziccid[21] = {0};
    cfg_get_item("ziccid", ziccid, sizeof(ziccid));

	cfg_get_item(NV_MODEM_MAIN_STATE, mmi_nv_value, sizeof(mmi_nv_value));

	if (strncmp("00000000000000000000", ziccid, sizeof(ziccid)) == 0) {
		net_mode = NET_MODE_NOTREADY;
	}

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo: salvikie net_mode=%d \n", net_mode);
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
	char ppp_status[32] = {0};
	cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
	if(0 == strcmp(ppp_status, "ppp_connecting"))
	{
		pNetInfo->connect_status = NET_STATE_CONNECTING;
	}
	//获取限速值
	int ret;
    UINT64 down_limit = 0L;
    char down_limit_str[21] = {0};
    ret = cfg_get_item("tc_downlink", down_limit_str, sizeof(down_limit_str));
    if (ret != 0)
    {
        slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo: get tc_downlink failed, ret=%d\n", ret);
    }
	//这里的down_limit是Byte
    down_limit = atoll(down_limit_str);
	//如果小于50Kb,就把connect_status设置为NET_STATE_LIMIT_SPEED
	if(down_limit > 0 && down_limit*8<50000 && pNetInfo->connect_status == NET_STATE_CONNECTED)
	{
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo: down_limit=%llu, set connect_status to NET_STATE_LIMIT_SPEED\n", down_limit);
		pNetInfo->connect_status = NET_STATE_LIMIT_SPEED;
	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo: down_limit=%llu, connect_status remains unchanged\n", down_limit);
	}
#endif
#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
	cfg_get_item("qrzl_user_net_disconn", mmi_nv_value, sizeof(mmi_nv_value));
	if (strcmp(mmi_nv_value, "1") == 0) {
		pNetInfo->connect_status = NET_STATE_WITHOUT_BALANCE;
	}
	if (strncmp("00000000000000000000", ziccid, sizeof(ziccid)) == 0) {
		pNetInfo->connect_status = NET_STATE_DISCONNECTED;
	}
#endif
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetInfo: salvikie connect_status=%d \n", pNetInfo->connect_status);
#endif

	
	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_WAN) / sizeof(T_zMmiNetLedConfig); ++ i) {
		if ((net_mode == g_mmi_netled_config_tab[i].net_info.net_mode) && (customer_type == g_mmi_netled_config_tab[i].custom_type)) {
			if (net_mode == NET_MODE_NOSERVICE 
				|| net_mode == NET_MODE_LIMITSERVICE
				|| net_mode == NET_MODE_NOTREADY) {
				mmi_setLedShowInfo(pLedInfo, g_mmi_netled_config_tab[i].led_info);
				return MMI_SUCCESS;
			} else {
				if ((pNetInfo->connect_status == g_mmi_netled_config_tab[i].net_info.connect_status) && (pNetInfo->socket_state == g_mmi_netled_config_tab[i].net_info.socket_state)) {
					mmi_setLedShowInfo(pLedInfo, g_mmi_netled_config_tab[i].led_info);
					slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_getLedNetInfo netmode = %d, con_sta = %d,i = %d,ledsta = %d\n", net_mode, pNetInfo->connect_status, i, g_mmi_netled_config_tab[i].led_info.led_state);
					return MMI_SUCCESS;
				}
			}
		}
	}
	return MMI_ERROR;
}

/**********************************************************************************
*��������:��ȡ�����źŸ���Ϣ��ҢԶcpe��
***********************************************************************************/
SINT32 mmi_getLedNetSigInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMMINetInfo *pNetInfo = (T_zMMINetInfo *)taskInfo;
	UINT32 i = 0;
	int customer_type = g_customer_type; 
	E_zMmi_Net_Mode net_mode = pNetInfo->net_mode;
	char mmi_nv_value[32] = {0};

	cfg_get_item("mmi_led_plan", mmi_nv_value, sizeof(mmi_nv_value));
	if (0 == strcmp(mmi_nv_value, "yaoyuan")) {
		customer_type = CUSTOMER_YAOYUAN;
	}

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedNetSigInfo net_mode=%d signal_num %d customer_type=%d\n",pNetInfo->net_mode,pNetInfo->signal_num,customer_type);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_SIGNAL) / sizeof(T_zMmiNetLedConfig); ++ i) {
		if ((net_mode == g_mmi_signalled_config_tab[i].net_info.net_mode) && (customer_type == g_mmi_signalled_config_tab[i].custom_type)) {
			if (net_mode == NET_MODE_NOSERVICE 
				|| net_mode == NET_MODE_LIMITSERVICE
				|| net_mode == NET_MODE_NOTREADY) {
				mmi_setLedShowInfo(pLedInfo, g_mmi_signalled_config_tab[i].led_info);
				return MMI_SUCCESS;
			} else {
				if (pNetInfo->signal_num == g_mmi_signalled_config_tab[i].net_info.signal_num) {
					mmi_setLedShowInfo(pLedInfo, g_mmi_signalled_config_tab[i].led_info);
					slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_getLedNetSigInfo netmode = %d, con_sta = %d,i = %d,ledsta = %d\n", net_mode, pNetInfo->connect_status, i, g_mmi_netled_config_tab[i].led_info.led_state);
					return MMI_SUCCESS;
				}
			}
		}
	}
	return MMI_ERROR;
}

/**********************************************************************************
*��������:��ȡWIFI����Ϣ
***********************************************************************************/
SINT32 mmi_getLedWifiInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMMIWifiInfo * pWifiInfo = (T_zMMIWifiInfo *)taskInfo;
	UINT32 i = 0;
	
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedWifiInfo wifi_state=%d, wifidata_state=%d\n",pWifiInfo->wifi_state,pWifiInfo->wifidata_state);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_LAN) / sizeof(T_zMmiWifiLedConfig); ++ i) {
		if (pWifiInfo->wifi_state == g_mmi_wifiled_config_tab[i].wifi_info.wifi_state
		    && pWifiInfo->wifidata_state == g_mmi_wifiled_config_tab[i].wifi_info.wifidata_state
		    && (g_customer_type == g_mmi_wifiled_config_tab[i].custom_type)) {
			mmi_setLedShowInfo(pLedInfo, g_mmi_wifiled_config_tab[i].led_info);
			return MMI_SUCCESS;
		}
	}
	
	return MMI_ERROR;
}

/**********************************************************************************
*��������:��ȡwps����Ϣ ҢԶcpe
***********************************************************************************/
SINT32 mmi_getLedWpsInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
	T_zMMIWifiInfo * pWifiInfo = (T_zMMIWifiInfo *)taskInfo;
	UINT32 i = 0;

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedWpsInfo wps_state=%d customer_type=%d\n",pWifiInfo->wps_state, g_customer_type);

	for (i = 0; i < (UINT32)mmi_get_config_tab_size(LED_WPS) / sizeof(T_zMmiWifiLedConfig); ++ i) {
		if (pWifiInfo->wps_state == g_mmi_wpsled_config_tab[i].wifi_info.wps_state
		    && (g_customer_type == g_mmi_wpsled_config_tab[i].custom_type)) {
			mmi_setLedShowInfo(pLedInfo, g_mmi_wpsled_config_tab[i].led_info);
			return MMI_SUCCESS;
		}
	}

	return MMI_ERROR;
}

/**********************************************************************************
*��������:�����澯˫����
***********************************************************************************/
VOID mmi_set_led_double_blink()
{
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_set_led_double_blink 1000ms off\n");

	T_zMmi_Led_Info userInfo = {0};
	userInfo.led_name = LED_WAN;
	userInfo.led_state = LED_STATE_OFF;
	userInfo.led_color = LED_COLOR_YELLOW;
	mmi_led_operate(&userInfo);
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);
	mmi_startLedTrafficWarnTimer(1000, 0);

	int sem_ret1 = -1;
	sem_ret1 = mmi_GetSemaphoreEintr(&g_mmi_traffic_warn_sem_id, MMI_WAIT_FOREVER);
	if (sem_ret1 != MMI_SUCCESS) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI DBget1 FAILED! errno=%d\n",errno);
	}
	userInfo.led_color = LED_COLOR_RED;
	userInfo.led_state = LED_STATE_ON;
	mmi_led_operate(&userInfo);
	mmi_startLedTrafficWarnTimer(100, 0);

	int sem_ret2 = -1;
	sem_ret2 = mmi_GetSemaphoreEintr(&g_mmi_traffic_warn_sem_id, MMI_WAIT_FOREVER);
	if (sem_ret2 != MMI_SUCCESS) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI DBget2 FAILED! errno=%d\n",errno);
	}

	userInfo.led_state = LED_STATE_OFF;
	mmi_led_operate(&userInfo);
	mmi_startLedTrafficWarnTimer(100, 0);

	int sem_ret3 = -1;
	sem_ret3 = mmi_GetSemaphoreEintr(&g_mmi_traffic_warn_sem_id, MMI_WAIT_FOREVER);
	if (sem_ret3 != MMI_SUCCESS) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI DBget3 FAILED! errno=%d\n",errno);
	}

	userInfo.led_state = LED_STATE_ON;
	mmi_led_operate(&userInfo);
	mmi_startLedTrafficWarnTimer(100, 0);

	int sem_ret4 = -1;
	sem_ret4 = mmi_GetSemaphoreEintr(&g_mmi_traffic_warn_sem_id, MMI_WAIT_FOREVER);
	if (sem_ret4 != MMI_SUCCESS) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI DBget4 FAILED! errno=%d\n",errno);
	}

	userInfo.led_state = LED_STATE_OFF;
	mmi_led_operate(&userInfo);
	mmi_startLedTrafficWarnTimer(1000, 0);

	int sem_ret5 = -1;
	sem_ret5 = mmi_GetSemaphoreEintr(&g_mmi_traffic_warn_sem_id, MMI_WAIT_FOREVER);
	if (sem_ret5 != MMI_SUCCESS) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI DBget5 FAILED! errno=%d\n",errno);
	}

	mmi_set_update_flag(MMI_TASK_NET);
	mmi_startLedTrafficWarnTimer(4000, 1);
}
/**********************************************************************************
*��������:�������������־
***********************************************************************************/
VOID mmi_set_traffic_warn_flag(BOOL flag)
{
	s_mmi_trafficled_warn_flag = flag;
}
/**********************************************************************************
*��������:��ȡ��������Ϣ
***********************************************************************************/
SINT32 mmi_getLedTrafficInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMMITrafficInfo * pTrafficInfo = (T_zMMITrafficInfo *)taskInfo;

#if 0
	pLedInfo->led_name = IOCTL_LED_TRAFFIC;
	pLedInfo->userPara.uBrightness = 0;
	pLedInfo->userPara.uColor = LED_COLOR_GREEN;
	pLedInfo->userPara.uSleep = LED_SLEEP_MAX;
	if (pTrafficInfo->traffic_switch == TRAFFIC_LIMIT_SWITCH_OFF) {
		pLedInfo->userPara.uState = LED_STATE_OFF;
		pLedInfo->speed = LED_STATE_DEFAULT_BLINK;
		pLedInfo->userPara.uTraffic = LED_TRAFFIC_100;
	} else {
		if (pTrafficInfo->warning_tip_flag == 0) {
			pLedInfo->userPara.uState = LED_STATE_ON;
			pLedInfo->speed = LED_STATE_DEFAULT_BLINK;
		} else {
			pLedInfo->userPara.uState = LED_STATE_BLINK;
			pLedInfo->speed = LED_STATE_FAST_BLINK;
		}
	}
#endif

	UINT32 temp = 100 * (pTrafficInfo->uesd_traffic / pTrafficInfo->total_traffic);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedTrafficInfo temp = %d, level = %d\n", temp, pTrafficInfo->warning_tip_level);
	if ((pTrafficInfo->warning_tip_level != 0) && (temp >= pTrafficInfo->warning_tip_level) && (pTrafficInfo->traffic_switch == TRAFFIC_LIMIT_SWITCH_ON)) {
		if (!s_mmi_trafficled_warn_flag) {
			mmi_set_traffic_warn_flag(TRUE);
			mmi_set_led_double_blink();
		}
	} else {
		mmi_set_traffic_warn_flag(FALSE);
		mmi_stopLedTrafficWarnTimer();
		mmi_set_update_flag(MMI_TASK_NET);
	}
	return MMI_ERROR;

}

static VOID mmi_set_allleds_off(E_zMMI_BatLed_Mode mode)
{
	T_zMmi_Led_Info userInfo = {0};

	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_set_allleds_off mode = %d\n", mode);
	mmi_set_traffic_warn_flag(FALSE);
	mmi_stopLedTrafficWarnTimer();
	mmi_clean_update_flag(MMI_TASK_NET);
	mmi_clean_update_flag(MMI_TASK_TRAFFIC);
	mmi_clean_update_flag(MMI_TASK_WIFI);
	
	mmi_clean_update_flag(MMI_TASK_NETSIGNAL);
	mmi_clean_update_flag(MMI_TASK_LED_WPS);
	mmi_clean_update_flag(MMI_TASK_RJ11);

	if (g_voicetask_enable)
		mmi_clean_update_flag(MMI_TASK_VOIP);
	else if (g_smstask_enable)
		mmi_clean_update_flag(MMI_TASK_SMS);

	if (mode == MODE_OFF) {
		userInfo.led_name = LED_BATTERY;
		userInfo.led_color = LED_COLOR_YELLOW;
		userInfo.led_state = LED_STATE_OFF;

		mmi_led_operate(&userInfo);
	} else if (mode == MODE_CHARGING) {

	} else if (mode == MODE_STANDBY) {
		userInfo.led_name = LED_BATTERY;
		userInfo.led_color = LED_COLOR_GREEN;
		userInfo.led_state = LED_STATE_BLINK;
		userInfo.ledBlink_time.uBlinkOffTime = LED_BREATH_BLINK_OFF_TIME;
		userInfo.ledBlink_time.uBlinkOnTime = LED_BREATH_BLINK_ON_TIME;
		mmi_led_operate(&userInfo);
	}
	userInfo.led_name = LED_WAN;
	userInfo.led_state = LED_STATE_OFF;
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_LAN;
	mmi_led_operate(&userInfo);
	if (g_voicetask_enable)
		userInfo.led_name = LED_VOIP;
	else if (g_smstask_enable)
		userInfo.led_name = LED_SMS;
	mmi_led_operate(&userInfo);
	//yaoyuan cpe
	userInfo.led_color = LED_COLOR_BLUE;
	userInfo.led_name = LED_WPS;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_RJ11;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_SIGNAL;
	mmi_led_operate(&userInfo);
}

/**********************************************************************************
��������:�ػ�������ʱ���е���ȫ����ȫ��
***********************************************************************************/
VOID mmi_set_allleds_blink(VOID)
{

	mmi_set_traffic_warn_flag(FALSE);
	mmi_stopLedTrafficWarnTimer();
	mmi_clean_update_flag(MMI_TASK_NET);
	mmi_clean_update_flag(MMI_TASK_TRAFFIC);
	mmi_clean_update_flag(MMI_TASK_WIFI);
	//yao yuan
	mmi_clean_update_flag(MMI_TASK_NETSIGNAL);
	mmi_clean_update_flag(MMI_TASK_LED_WPS);
	mmi_clean_update_flag(MMI_TASK_RJ11);

	if (g_voicetask_enable)
		mmi_clean_update_flag(MMI_TASK_VOIP);
	else if (g_smstask_enable)
		mmi_clean_update_flag(MMI_TASK_SMS);

	T_zMmi_Led_Info userInfo = {0};

#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
	system("echo OFF > /dev/charlieplex_display");
#else
	userInfo.led_name = LED_BATTERY;
	userInfo.led_state = LED_STATE_ON;
	userInfo.led_color = LED_COLOR_GREEN;
	userInfo.traffic = TRAFFIC_LED_4;
	mmi_led_operate(&userInfo);
#endif
#else
	userInfo.led_name = LED_BATTERY;
	userInfo.led_state = LED_STATE_ON;
	userInfo.led_color = LED_COLOR_RED;
	mmi_led_operate(&userInfo);
#endif
	

	userInfo.led_name = LED_LAN;
#if defined(JCV_HW_MZ803_V3_2) || defined(JCV_HW_UZ901_V1_4)
	userInfo.led_color = LED_COLOR_GREEN;
#else
	userInfo.led_color = LED_COLOR_BLUE;
#endif
	mmi_led_operate(&userInfo);

	if (g_voicetask_enable)
		userInfo.led_name = LED_VOIP;
	else if (g_smstask_enable)
		userInfo.led_name = LED_SMS;
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);

	userInfo.led_name = LED_WAN;
	if(g_customer_type == CUSTOMER_SDK_MIN)
		userInfo.led_color = LED_COLOR_GREEN;
	else
		userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);

	//yaoyuan cpe
	userInfo.led_color = LED_COLOR_BLUE;
	userInfo.led_name = LED_WPS;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_RJ11;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_SIGNAL;
	userInfo.traffic = TRAFFIC_LED_5;
	mmi_led_operate(&userInfo);


	mmi_sleep(LED_BLINK_TIME_INTERVAL);

#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
	userInfo.led_state = LED_STATE_ON;
	userInfo.led_name = LED_BATTERY;
	userInfo.led_color = LED_COLOR_GREEN;
	//use off all battery led
	userInfo.traffic = TRAFFIC_LED_5;
	mmi_led_operate(&userInfo);
	userInfo.led_state = LED_STATE_OFF;
#else
	userInfo.led_state = LED_STATE_OFF;
	userInfo.led_name = LED_BATTERY;
	userInfo.led_color = LED_COLOR_RED;
	mmi_led_operate(&userInfo);
#endif

	userInfo.led_name = LED_LAN;
#if defined(JCV_HW_MZ803_V3_2) || defined(JCV_HW_UZ901_V1_4)
	userInfo.led_color = LED_COLOR_GREEN;
#else
	userInfo.led_color = LED_COLOR_BLUE;
#endif
	mmi_led_operate(&userInfo);

	if (g_voicetask_enable)
		userInfo.led_name = LED_VOIP;
	else if (g_smstask_enable)
		userInfo.led_name = LED_SMS;
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);

	userInfo.led_name = LED_WAN;
	userInfo.led_color = LED_COLOR_BLUE;
	mmi_led_operate(&userInfo);
	//yaoyuan cpe
	userInfo.led_color = LED_COLOR_BLUE;
	userInfo.led_name = LED_WPS;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_RJ11;
	mmi_led_operate(&userInfo);
	userInfo.led_name = LED_SIGNAL;
	mmi_led_operate(&userInfo);
	
	slog(MMI_PRINT, SLOG_DEBUG, "mmi_set_allleds_blink  off!\n");
}
SINT32 mmi_getLedCtrlInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMMICtrlInfo * pCtrlInfo = (T_zMMICtrlInfo *)taskInfo;
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;

	pLedInfo->traffic = TRAFFIC_LED_MAX;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLedCtrlInfo pCtrlInfo->ledmode = %d\n", pCtrlInfo->ledmode);
	switch (pCtrlInfo->ledmode) {
	case MMI_ACTIVE_MODE:
		mmi_set_update_flag(MMI_TASK_BATTERY);

		if (g_voicetask_enable)
			mmi_set_update_flag(MMI_TASK_VOIP);
		else if (g_smstask_enable)
			mmi_set_update_flag(MMI_TASK_SMS);

		mmi_set_update_flag(MMI_TASK_WIFI);
		mmi_set_update_flag(MMI_TASK_NET);
		mmi_set_update_flag(MMI_TASK_TRAFFIC);
		mmi_set_update_flag(MMI_TASK_LED_WPS);
		break;
	case MMI_IDLE_LEDOFF_MODE:
		if (g_customer_type == CUSTOMER_SDK || g_customer_type == CUSTOMER_SDK_MIN) {
			mmi_set_allleds_off(MODE_OFF);
		}		
		set_wake_unlock(MMI_MAIN_LOCK_ID);
		break;
	case MMI_IDLE_STANDBY_LEDOFF_MODE:
		if (g_customer_type == CUSTOMER_SDK || g_customer_type == CUSTOMER_SDK_MIN) {
			mmi_set_allleds_off(MODE_STANDBY);
		}		
		set_wake_unlock(MMI_MAIN_LOCK_ID);
		break;
	case MMI_FAKE_POWEROFF_MODE:
		mmi_set_allleds_off(MODE_OFF);
		set_wake_unlock(MMI_MAIN_LOCK_ID);
		break;
	case MMI_IDLE_CHG_LEDOFF_MODE:
		if (g_customer_type == CUSTOMER_SDK || g_customer_type == CUSTOMER_SDK_MIN) {
			mmi_set_allleds_off(MODE_CHARGING);
		}
		break;
	case MMI_FAKE_POWEROFF_CHARGE_MODE:
		mmi_set_update_flag(MMI_TASK_BATTERY);
		mmi_set_allleds_off(MODE_CHARGING);
		break;
	case MMI_POWEROFF_MODE:
	case MMI_RESET_MODE:
	case MMI_RESTART_MODE:
		if(g_customer_type == CUSTOMER_SDK || g_customer_type == CUSTOMER_SDK_MIN){
			mmi_set_allleds_blink();
		}
#ifndef DISABLE_LCD
		if (g_PowerOnOffFun != NULL) {
			slog(MMI_PRINT, SLOG_DEBUG, "mmi_set_allleds_blink  g_PowerOnOffFun!\n");
			g_PowerOnOffFun();
		}
#endif
		break;
	case MMI_FAST_POWERON_MODE:
		mmi_set_allleds_blink();
		mmi_set_update_flag(MMI_TASK_BATTERY);

		if (g_voicetask_enable)
			mmi_set_update_flag(MMI_TASK_VOIP);
		else if (g_smstask_enable)
			mmi_set_update_flag(MMI_TASK_SMS);

		mmi_set_update_flag(MMI_TASK_WIFI);
		mmi_set_update_flag(MMI_TASK_NET);
		mmi_set_update_flag(MMI_TASK_TRAFFIC);
		mmi_set_update_flag(MMI_TASK_LED_WPS);
		break;
	case MMI_POWEROFF_ON_MODE:
		break;
	default:
		break;
	}
	return MMI_ERROR;
}

SINT32 mmi_getLedPowerOffChagerInfo(UINT32 taskInfo, UINT32 outLedInfo)
{
	T_zMmi_Poc_Info *pPocInfo = (T_zMmi_Poc_Info *)taskInfo;
	T_zMmi_Led_Info* pLedInfo = (T_zMmi_Led_Info*)outLedInfo;
#ifdef JCV_CHARLIEPLEX_LED_DIGITS
	// 关机充电时在数码管显示电量的变量声明
	static SINT32 s_last_poweroff_bat_pers = -1;
	static BOOL s_last_poweroff_L1_was_on = FALSE;
	static BOOL s_is_poweroff_first_call = TRUE;
	char buf[64] = {0};
	SINT32 voltagepower = 0;
	SINT32 battery_percent = 0;
#endif

	pLedInfo->led_name = LED_BATTERY;
	pLedInfo->traffic = TRAFFIC_LED_MAX;
	pLedInfo->led_color = LED_COLOR_GREEN;

#ifdef JCV_CHARLIEPLEX_LED_DIGITS
#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
	/* 如果数码管处于休眠状态，则不更新显示 */
	if (s_digits_sleeping) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: digits is sleeping, skip display update\n");
		goto skip_poweroff_digits_display;
	}
#endif

	// 获取电压值
	voltagepower = offchg_voltage_state_read();
	if (voltagepower > 0) {
		// 根据电压计算电量百分比
		battery_percent = get_charging_voltage_level_from_table(voltagepower,
			(pPocInfo->poc_sta == POC_STATE_CHARGING || pPocInfo->poc_sta == POC_STATE_FULL));

		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: voltage=%d, percent=%d, poc_sta=%d\n",
			voltagepower, battery_percent, pPocInfo->poc_sta);

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
		/* 如果需要强制刷新，重置显示状态 */
		if (s_digits_force_refresh) {
			s_is_poweroff_first_call = TRUE;
			s_last_poweroff_bat_pers = -1;
			s_last_poweroff_L1_was_on = FALSE;
			s_digits_force_refresh = FALSE;
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: force refresh digits display\n");
		}
#endif

		// 根据充电状态显示不同的图标和电量
		if (pPocInfo->poc_sta == POC_STATE_CHARGING) {
			// 充电状态：显示闪电图标 + 电量百分比
			if (s_is_poweroff_first_call || battery_percent != s_last_poweroff_bat_pers || !s_last_poweroff_L1_was_on) {
				snprintf(buf, sizeof(buf), "echo L1ONL2ON%d > /dev/charlieplex_display", battery_percent);
				system(buf);
				s_last_poweroff_bat_pers = battery_percent;
				s_last_poweroff_L1_was_on = TRUE;
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: charging display %d%%\n", battery_percent);
			}
		} else if (pPocInfo->poc_sta == POC_STATE_FULL) {
			// 充满状态：显示百分号 + 100%
			if (s_is_poweroff_first_call || battery_percent != s_last_poweroff_bat_pers || s_last_poweroff_L1_was_on) {
				snprintf(buf, sizeof(buf), "echo L1OFFL2ON100 > /dev/charlieplex_display");
				system(buf);
				s_last_poweroff_bat_pers = 100;
				s_last_poweroff_L1_was_on = FALSE;
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: full display 100%%\n");
			}
		} else if (pPocInfo->poc_sta == POC_STATE_LOWBATTERY) {
			// 低电量状态：只显示电量百分比，不显示图标
			if (s_is_poweroff_first_call || battery_percent != s_last_poweroff_bat_pers || s_last_poweroff_L1_was_on) {
				snprintf(buf, sizeof(buf), "echo L1OFFL2OFF%d > /dev/charlieplex_display", battery_percent);
				system(buf);
				s_last_poweroff_bat_pers = battery_percent;
				s_last_poweroff_L1_was_on = FALSE;
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: low battery display %d%%\n", battery_percent);
			}
		} else {
			// 其他状态：只显示电量百分比
			if (s_is_poweroff_first_call || battery_percent != s_last_poweroff_bat_pers) {
				snprintf(buf, sizeof(buf), "echo L1OFFL2OFF%d > /dev/charlieplex_display", battery_percent);
				system(buf);
				s_last_poweroff_bat_pers = battery_percent;
				s_last_poweroff_L1_was_on = FALSE;
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI poweroff charger: other state display %d%%\n", battery_percent);
			}
		}

		s_is_poweroff_first_call = FALSE;

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
		/* 重新启动休眠定时器 */
		if (s_digits_timer_running) {
			mmi_digits_sleep_timer_stop();
		}
		mmi_digits_sleep_timer_start();
		s_digits_timer_running = TRUE;
#endif
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI poweroff charger: failed to read voltage\n");
	}

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
skip_poweroff_digits_display:
#endif
#endif // JCV_CHARLIEPLEX_LED_DIGITS

	// 原有的LED控制逻辑
	if (pPocInfo->poc_sta == POC_STATE_FULL || pPocInfo->poc_sta == POC_STATE_TEMP_ERROR) {
		pLedInfo->led_state = LED_STATE_ON;
	}
	else if (pPocInfo->poc_sta == POC_STATE_LOWBATTERY) {
		pLedInfo->led_color = LED_COLOR_RED;
		pLedInfo->led_state = LED_STATE_ON;
	}
	else {
		pLedInfo->led_state = LED_STATE_BLINK;
		pLedInfo->ledBlink_speed = LED_STATE_BAT_BLINK;
	}
	return MMI_SUCCESS;

}

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
/**********************************************************************************
函数名称:数码管进入休眠状态
***********************************************************************************/
VOID mmi_digits_sleep(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_sleep: digits entering sleep mode\n");

	/* 关闭数码管显示 */
	system("echo OFF > /dev/charlieplex_display");

	/* 设置休眠状态 */
	s_digits_sleeping = TRUE;
	s_digits_timer_running = FALSE;

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_sleep: digits display turned off\n");
}

/**********************************************************************************
函数名称:唤醒数码管显示
***********************************************************************************/
VOID mmi_digits_wakeup(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_wakeup: waking up digits display\n");

	/* 停止之前的休眠定时器 */
	if (s_digits_timer_running) {
		mmi_digits_sleep_timer_stop();
		s_digits_timer_running = FALSE;
	}

	/* 设置为唤醒状态 */
	s_digits_sleeping = FALSE;

	/* 设置强制刷新标志，确保重新显示充电图标和百分比 */
	s_digits_force_refresh = TRUE;

	/* 强制更新电池信息显示 */
	mmi_set_update_flag(MMI_TASK_BATTERY);

	/* 启动20秒休眠定时器 */
	mmi_digits_sleep_timer_start();
	s_digits_timer_running = TRUE;

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_digits_wakeup: digits display woken up, 20s timer started\n");
}



/**********************************************************************************
函数名称:判断数码管是否处于休眠状态
***********************************************************************************/
BOOL mmi_is_digits_sleeping(VOID)
{
	return s_digits_sleeping;
}
#endif

