/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
******************************************************************************/

/*****************************************************************************
 ͷ�ļ�
******************************************************************************/
#include "mmi_common.h"
#include "mmi_lcd.h"

//���涼��debug��λ����ʱ�õ���ȫ�ֱ���
#define MMI_DEBUG_MAINCONTROL
#ifdef MMI_DEBUG_MAINCONTROL
SINT32 g_mmi_MainContrlIndex = -1;
SINT32 g_mmi_MainContrlProcess = -1;//0, get content;1 gelceinfo; 2 show
SINT32 g_mmi_MainContrl = -1;


int g_debug[20] = {0};
int g_debug_index = 0;
#endif
/********************************************************************************
  ȫ�ֱ�������
**********************************************************************************/
extern E_zMmiShowMode g_showMode;

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
extern BOOL s_digits_timer_running; // 数码管休眠定时器是否正在运行
#endif

/********************************************************************************
  ȫ�ֱ�������
**********************************************************************************/
int g_mmi_msgQue_Id = 0;
pthread_mutex_t g_mmi_lcdmode_mutex ;
pthread_mutex_t g_mmi_ledmode_mutex ;
pthread_mutex_t g_mmi_update_flag_mutex;
pthread_mutex_t g_mmi_refresh_lcd_mutex;
pthread_mutex_t g_mmi_poweron_mutex;

sem_t g_mmi_update_sem_id;
sem_t g_mmi_init_sem_id;
sem_t g_mmi_traffic_warn_sem_id;
sem_t g_mmi_poweroff_sem_id ;
sem_t g_mmi_gui_init_sem_id;


UINT32 	g_mmi_poweroff_turnon_flag = FALSE ;
UINT32  g_mmi_led_status_flag = 0;
T_zMMITaskInfoItem g_zMMITaskInfoTab[MMI_TASK_MAX] = {0};

UINT32 g_smstask_enable = 0;
UINT32 g_voicetask_enable = 0;
UINT32 g_temp_protect = 0;
UINT32 g_discharge_protect = 0;
UINT32 g_charge_protect = 0;

UINT32 g_fast_poweron = 0;
UINT32 g_led_standby_mode = 0;
UINT32 g_led_sleep_mode = 0;
UINT32 g_use_wifi_usernum = 0;

UINT32 g_show_pagefirst = 0;
UINT32 g_show_pagesecond = 0;
UINT32 g_show_pagethird = 0;
/*
SINT32 g_mmi_hightempvol = 0;
SINT32 g_mmi_superhightempvol = 0;
SINT32 g_mmi_lowtempvol = 0;
SINT32 g_mmi_superlowtempvol = 0;
*/
SINT32 g_mmi_batvoltageline[21] = {0};
#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
SINT32 g_mmi_chgvoltageline[21] = {0};
#endif


SINT32 g_mmi_power_mode = 0;//0:charge;1:poweron

//int g_customer_type = CUSTOMER_SDK;  // ��Բ�ͬ�ͻ��Ķ�������
extern int g_customer_type;

//���ô������±�־
VOID mmi_set_update_flag(E_zMmi_Task task)
{
	mmi_getMutex(&g_mmi_update_flag_mutex);
	g_zMMITaskInfoTab[task].is_update = 1;
	mmi_putMutex(&g_mmi_update_flag_mutex);

	mmi_PutSemaphore(&g_mmi_update_sem_id);
}

VOID mmi_clean_update_flag(E_zMmi_Task task)
{
	mmi_getMutex(&g_mmi_update_flag_mutex);
	g_zMMITaskInfoTab[task].is_update = 0;
	mmi_putMutex(&g_mmi_update_flag_mutex);
}
SINT32 mmi_get_update_flag(E_zMmi_Task task)
{
	SINT32 ret = 0;
	if (task >= MMI_TASK_MAX)//cov kw 3
		return ret;
	mmi_getMutex(&g_mmi_update_flag_mutex);
	ret = g_zMMITaskInfoTab[task].is_update;
	g_zMMITaskInfoTab[task].is_update = 0;
	mmi_putMutex(&g_mmi_update_flag_mutex);
	return ret;
}

//��ʾled��
static VOID mmi_show_led_item(T_zMMITaskInfoItem* taskInfoItem)
{
	if (taskInfoItem->get_taskinfo_fun == NULL || taskInfoItem->taskinfo == NULL ||
	    taskInfoItem->get_ledinfo_fun == NULL || taskInfoItem->ledinfo == NULL) {
		//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI mmi_show_led parameter null!!!\n");
		return;
	}
	if (taskInfoItem->get_taskinfo_fun((UINT32)taskInfoItem->taskinfo) == MMI_SUCCESS) { //e64
		if (taskInfoItem->get_ledinfo_fun((UINT32)taskInfoItem->taskinfo, (UINT32)taskInfoItem->ledinfo) == MMI_SUCCESS) {
			mmi_showLed((UINT32)taskInfoItem->ledinfo);
		}
	}
}
//��ʾlcd��
static VOID mmi_show_lcd_item(T_zMMITaskInfoItem* taskInfoItem)
{
	if (taskInfoItem->get_taskinfo_fun == NULL || taskInfoItem->taskinfo == NULL ||
	    taskInfoItem->get_lcdinfo_fun == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_show_lcd parameter null!!!");
		return;
	}
#ifdef MMI_DEBUG_MAINCONTROL
	g_mmi_MainContrlProcess = 0;//0, get content;1 gelceinfo; 2 show
#endif
	if (taskInfoItem->get_taskinfo_fun((UINT32)taskInfoItem->taskinfo) == MMI_SUCCESS) {
#ifdef MMI_DEBUG_MAINCONTROL
		g_mmi_MainContrlProcess = 1;//0, get content;1 gelceinfo; 2 show
#endif
		if (taskInfoItem->get_lcdinfo_fun((UINT32)taskInfoItem->taskinfo) == MMI_SUCCESS) {
#ifdef MMI_DEBUG_MAINCONTROL
			g_mmi_MainContrlProcess = 2;//0, get content;1 gelceinfo; 2 show
#endif
			//mmi_showLcd(appInfoItem->lcdinfo);
			mmi_invalidateLcd(taskInfoItem->taskinfo);
		}
	}
}
//MMI����
static VOID *mmi_mainControllerEntry(VOID *arg)
{
	UINT32 i = 0;
	int ret = 0;
	
#ifdef MMI_DEBUG_MAINCONTROL
	g_mmi_MainContrl = 1;
#endif

#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		while ((ret = mmi_GetSemaphore(&g_mmi_init_sem_id, MMI_WAIT_FOREVER)) != MMI_SUCCESS) {
			slog(MMI_PRINT, SLOG_DEBUG, "zte_mmi g_mmi_update_sem_id get failed ret = %d,errno=%d\n", ret, errno);
			continue;
		}
		mmi_DeleteSemaphore(&g_mmi_init_sem_id);
	}
#endif

	while (1) {
		if ((ret = mmi_GetSemaphore(&g_mmi_update_sem_id, MMI_WAIT_FOREVER)) != MMI_SUCCESS) {//kw 3
			slog(MMI_PRINT, SLOG_DEBUG, "zte_mmi mmi_mainControllerEntry failed ret = %d,errno=%d\n", ret, errno);
			continue;
		}
		for (i = 0; i < sizeof(g_zMMITaskInfoTab) / sizeof(T_zMMITaskInfoItem); ++ i) { //warning539
#ifdef MMI_DEBUG_MAINCONTROL
			g_mmi_MainContrlIndex = i;
#endif
			if (mmi_get_update_flag(g_zMMITaskInfoTab[i].task)  == 1) { //�и���
				if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) { //led
					mmi_show_led_item(&(g_zMMITaskInfoTab[i]));
				}
#ifndef DISABLE_LCD
				if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) { //lcd
					mmi_show_lcd_item(&(g_zMMITaskInfoTab[i]));
				}
#endif
			}
		}
#ifdef MMI_DEBUG_MAINCONTROL
		g_mmi_MainContrlIndex = -1;
		g_mmi_MainContrlProcess = -1;//0, get content;1 gelceinfo; 2 show
#endif
	}
}


//ע��MMIҵ����Ϣ
VOID mmi_register_taskinfo_item(T_zMMITaskInfoItem* taskInfoItem)
{
	if (taskInfoItem != NULL) {
		E_zMmi_Task task = taskInfoItem->task;//ҵ��
		g_zMMITaskInfoTab[task].task = taskInfoItem->task;
		mmi_getMutex(&g_mmi_update_flag_mutex); //cov m
		g_zMMITaskInfoTab[task].is_update = taskInfoItem->is_update;
		mmi_putMutex(&g_mmi_update_flag_mutex);
		//g_zMMIFrameInfoTab[appInfoItem->flag].show_mode= appInfoItem->show_mode;
		g_zMMITaskInfoTab[task].taskinfo = taskInfoItem->taskinfo;
		g_zMMITaskInfoTab[task].ledinfo = taskInfoItem->ledinfo;
		g_zMMITaskInfoTab[task].get_taskinfo_fun = taskInfoItem->get_taskinfo_fun;
		g_zMMITaskInfoTab[task].get_ledinfo_fun = taskInfoItem->get_ledinfo_fun;
		g_zMMITaskInfoTab[task].get_lcdinfo_fun = taskInfoItem->get_lcdinfo_fun;
	}
}

/********************************************************************************
  MMI LOG�����ļ�
**********************************************************************************/
static char * mmi_get_time(void)
{
	time_t timep;
	struct tm *p;
	static char buf[22];
	memset(buf, 0, 22);
	time(&timep);
	p = (struct tm *)localtime(&timep);
	if (NULL == p) {
		return NULL;
	}
	snprintf(buf, 21, "%4d/%02d/%02d %02d:%02d:%02d ", 1900 + p->tm_year, 1 + p->tm_mon, p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
	return buf;
}

void mmi_log_save(const char *fmt, ...)
{
	time_t timep;
	struct tm p = {0};
	va_list ap;
	FILE *fp = NULL;
	struct stat statbuff;

	time(&timep);
	localtime_r(&timep, &p);
#if MMI_SERIAL_DEBUG
	//printf("%4d/%02d/%02d %02d:%02d:%02d ",1900 + p.tm_year,1 + p.tm_mon,p.tm_mday,p.tm_hour,p.tm_min,p.tm_sec);
	va_start(ap, fmt);
	vprintf(fmt, ap);
	va_end(ap);
#endif

#if MMI_FILE_DEBUG
	fp = fopen(MMI_LOG_FILE_PATH, "a+");

	if (fp) {
		fprintf(fp, "%4d/%02d/%02d %02d:%02d:%02d ", 1900 + p.tm_year, 1 + p.tm_mon, p.tm_mday, p.tm_hour, p.tm_min, p.tm_sec);
		va_start(ap, fmt);
		vfprintf(fp, fmt, ap);
		va_end(ap);
		fclose(fp);
		if (stat(MMI_LOG_FILE_PATH, &statbuff) == 0) {
			if (statbuff.st_size >= MMI_MAX_LOG_LENGTH) {
				remove(MMI_LOG_OLD_FILE_PATH);
				rename(MMI_LOG_FILE_PATH, MMI_LOG_OLD_FILE_PATH);
			}
		}
	}
#endif
}


/**********************************************************************************
��������:��������MMI���̵���Ϣ
***********************************************************************************/
//zdm ��mmi�ܹ�������������Ϣ�����д����������ڲ�ͨ��nv�������Ƿ��������д���
//�����ǰ�����޷���������Ϣ��ֱ�ӷ��ؼ���
static int mmi_ProcMsg(MSG_BUF *pstMsg)
{
	int i = 0;
	if (NULL == pstMsg) {
		return -1;
	}
	//kw 3, kw bug???
	//if ((pstMsg->usMsgCmd != MSG_CMD_MMICHECK_TIP_INFO) && (pstMsg->usMsgCmd != MSG_CMD_MMIGET_WIFI_DATA)) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI Mmi_ProcMsg msg:src_id = 0x%04x,lMsgType = %d,usMsgCmd = 0x%08x,usDataLen = %d,data = %s\n", pstMsg->src_id, pstMsg->lMsgType, pstMsg->usMsgCmd, pstMsg->usDataLen, pstMsg->aucDataBuf);
	//}
	switch (pstMsg->usMsgCmd) {
	case MSG_CMD_CHANNEL_NETWORK_MODE:
		zMMI_Handle_Msg_Network_Mode((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MULTI_CONNECT_STATUS:
		zMMI_Handle_Msg_MultiConnect_Status((VOID *)pstMsg->aucDataBuf, pstMsg->src_id);
		break;
	case MSG_CMD_CHANNEL_CONNECT_STATUS:
		zMMI_Handle_Msg_Connect_Status((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_OUT_REG_GET_SIGNAL_NUM:
		zMMI_Handle_Msg_Signal_Num((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_SMS_STATUS_INFO_IND:
		zMMI_Handle_Msg_SmsBox_Sattus((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_VOIP_STATUS_INFO:
		zMMI_Handle_Msg_Voip_Status((VOID *)pstMsg->aucDataBuf);
		break; //cov m
	case MSG_CMD_RJ11_STATUS_INFO:
		zMMI_Handle_Msg_Rj11_Status((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MODIFY_SSID_KEY:
		zMMI_Handle_Msg_Get_SSID_Key((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MMIGET_WIFI_STANUM:
	case MSG_CMD_STA_COUNT_CHANGE:
		zMMI_Handle_Msg_Get_Wifi_StaNum((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MMISTART_BACKLIGHTOFF_TIMER:
		zMMI_Handle_Msg_BacklightOff((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_RESET_NOTIFY:
		zMMI_Handle_Msg_Factory_Reset((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_POWEROFF_NOTIFY:
		zMMI_Handle_Msg_Poweroff((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_RESTART_NOTIFY:
		zMMI_Handle_Msg_Restart((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_GET_NET_PROVIDER:
		zMMI_Handle_Msg_Get_Provider((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_GET_TRAFFIC_INFO_START:
		zMMI_Handle_Msg_Get_TafficInfo_Start((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_GET_TRAFFIC_INFO_END:
		zMMI_Handle_Msg_Get_TafficInfo_End((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_TRAFFIC_INFO_RESET:
		zMMI_Handle_Msg_TafficInfo_Reset((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MMICHECK_TIP_INFO:
		zMMI_Handle_Msg_Check_Tip_Info((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MMISHOW_SSID_INFO:
		zMMI_Handle_Msg_SSID_Show_Info((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_MMIGET_WIFI_DATA:
		zMMI_Handle_Msg_Get_Wifi_Data((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_POWEROFF_RSP:
		zMMI_Handle_Msg_Plugout((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_SIM_ABNORMAL_IND:
		zMMI_Handle_Msg_Simcard_Info((VOID *)pstMsg->aucDataBuf);
		break;
	case MSG_CMD_SOCKET_STATE_CHANGE:
		zMMI_Handle_Msg_Socket_Status((VOID *)pstMsg->aucDataBuf, pstMsg->src_id);
		break;
	case MSG_CMD_MCUSOCKET_STATE_CHANGE:
		zMMI_Handle_Msg_MCUSocket_Status((VOID *)pstMsg->aucDataBuf);
		break;
#if 0
	case MSG_CMD_AUTOTEST_KEY_REQ:
		if (g_mmi_power_mode == 1)
			zMMI_Handle_Msg_Atest_Key((VOID *)pstMsg->aucDataBuf);
		else
			zMMI_Handle_Msg_Atest_Chg((VOID *)pstMsg->aucDataBuf);
		break;
#endif
	}
	return 0;
#if 0
	for (i = 0; i < (SINT32)(sizeof(scmp_info) / sizeof(scmp_info[0])); i++) {
		if (pstMsg->usMsgCmd == scmp_info[i].id) {
			if (scmp_info[i].func_ptr != NULL) {
				scmp_info[i].func_ptr((VOID *)pstMsg->aucDataBuf);
			}
			return 0;
		}
	}
#endif

	return 0;
}


static void *mmi_handleMsgEntry(void *arg)
{
	long MsgType = 0;
	MSG_BUF MmiMsg;
	prctl(PR_SET_NAME, "mmihandlemsg", 0, 0, 0);
	while (1) {
		memset((VOID *)(&MmiMsg), 0, sizeof(MSG_BUF));//warning516
		if (-1 != mmi_RecvMsgFromQueue(g_mmi_msgQue_Id, &MmiMsg, MsgType)) {
			mmi_ProcMsg(&MmiMsg);
		}
	}
}

/**********************************************************************************
��������:����ʱ����ƵĶ���
		 LCD ��ʾ��������
		 LED ��������ȫ��Ϩ�� �����ĸ���ͬʱ��һ�� �ٸ���ʵ��״̬����
***********************************************************************************/
static VOID mmi_set_poweron_show(VOID)
{
	mmi_set_led_mode(MMI_POWERON_MODE);
	mmi_set_update_flag(MMI_TASK_CTRL);//����ģʽ ��ʾ��������
}

/**********************************************************************************
��������:mmi ҵ��ע��
***********************************************************************************/

//zdm ʹ��nv����ҵ���ע��ͳ�ʼ������ʱ��ʵ���Ƕ��ڿ����ϵ��ÿ��ҵ�񣬾��ṩһ��nv����
//����Ҫ����������ȡ������ʹ�õ�nv����������κ�����£�����Ҫ��ҵ��ֱ��ע��Ϳ���
//����ʹ��nv���ƣ�����MMI_TASK_CTRL����������Ӧ�����еĳ����¾���Ҫע��ͳ�ʼ��


static int mmi_task_register()
{
	UINT32 i = 0;
	char *nv_task_tab[NV_CONTENT_LEN] = {0};

	//zdm ��ע��ͳ�ʼ��ҵ��֮ǰ���Ƚ�ҵ��Ŀ¼�е�ÿ��ҵ������Ϊ��Ч
	for (i = 0; i < sizeof(g_zMMITaskInfoTab) / sizeof(T_zMMITaskInfoItem); ++ i) {
		g_zMMITaskInfoTab[i].task = MMI_TASK_INVALID;
	}

	//�������led/lcd����Ҫʹ�õ�ҵ�����������ע�ᣬע��ͳ�ʼ�������ڲ���������lcd��led

	cfg_get_item("mmi_task_tab", nv_task_tab, sizeof(nv_task_tab));

	if (strstr(nv_task_tab, "net_task")) {
		mmi_RegisterNetTaskInfoItem();
	}

	//Ӳ�����ƣ�����ҵ����ͬһ��Ӳ����,���nvʹ��sms��voice��������

	if (strstr(nv_task_tab, "sms_task") && strstr(nv_task_tab, "voice_task")) {
		assert(0);
	}

	if (strstr(nv_task_tab, "sms_task")) {
		mmi_RegisterSmsTaskInfoItem();
		g_smstask_enable = 1;
	} else if (strstr(nv_task_tab, "voice_task")) {
		mmi_RegisterVoipTaskInfoItem();
		g_voicetask_enable = 1;
	}

	//�Ƿ��е����������
	if (strstr(nv_task_tab, "battery_task")) {
		mmi_RegisterBatteryTaskInfoItem();
		
	}

	if (strstr(nv_task_tab, "ctrl_task")) {
		mmi_RegisterCtrlTaskInfoItem();
	}


	if (strstr(nv_task_tab, "wifi_task")) {
		mmi_RegisterWifiTaskInfoItem();
	}


	if (strstr(nv_task_tab, "traffic_task")) {
		mmi_RegisterTrafficTaskInfoItem();
	}

	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		//ע����led��ص�ҵ�� �������ʵ��
		if (strstr(nv_task_tab, "netsignal_task")) {
			mmi_RegisterNetSigTaskInfoItem();
		}
		if (strstr(nv_task_tab, "ledwps_task")) {
			mmi_RegisterWpsTaskInfoItem();
		}
		if (strstr(nv_task_tab, "rj11_task")) {
			mmi_RegisterRj11TaskInfoItem();
		}
	}

#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		//ע����lcd��ص�ҵ�������ʵ��
		if (strstr(nv_task_tab, "tip_task")) {
			mmi_RegisterTipTaskInfoItem();
		}
		//Ӳ���е����Ƶ������£����ܴ�

		if (strstr(nv_task_tab, "power_task")) {
			mmi_RegisterPowerTaskInfoItem();
		}
		//֧��wifi������²���֧��
		if (strstr(nv_task_tab, "ssid_task")) {
			mmi_RegisterSSIDInfoTaskInfoItem();
		}
		if (strstr(nv_task_tab, "wificode_task")) {
			mmi_RegisterWifiCodeTaskInfoItem();
		}
		if (strstr(nv_task_tab, "tipnetconnect_task")) {
			mmi_RegisterTipNetConnectTaskInfoItem();
		}

		if (strstr(nv_task_tab, "tipwps_task")) {
			mmi_RegisterTipWpsTaskInfoItem();
		}
		if (strstr(nv_task_tab, "tipfota_task")) {
			mmi_RegisterTipFotaTaskInfoItem();

		}
		if (strstr(nv_task_tab, "tipwifistation_task")) {
			mmi_RegisterTipWifiStationConnectTaskInfoItem();
		}
	}
#endif
	return 0;
}

/**********************************************************************************
��������:mmi ҵ���ʼ��
***********************************************************************************/

//������������Ƶ�ĳЩҵ����ȡ��nv���ƣ�����������д򿪺͹ر�����
static int mmi_task_init()
{
	char *nv_task_tab[NV_CONTENT_LEN] = {0};


	//�������led/lcd����Ҫʹ�õ�ҵ����������ɳ�ʼ��

	cfg_get_item("mmi_task_tab", nv_task_tab, sizeof(nv_task_tab));


	if (strstr(nv_task_tab, "net_task")) {
		mmi_net_init();
	}

	//Ӳ�����ƣ�����ҵ����ͬһ��Ӳ����,���nvʹ��sms��voice��������
	if (strstr(nv_task_tab, "sms_task")) {
		mmi_sms_init();

	} else if (strstr(nv_task_tab, "voice_task")) {
		mmi_voip_init();

	}

	//�Ƿ��е����������
	if (strstr(nv_task_tab, "battery_task")) {
		mmi_battery_init();

#ifdef QRZL_DIGITS_SLEEP_WAKEUP_FEATURE
		mmi_digits_sleep_timer_start();
		s_digits_timer_running = TRUE;
#endif

	}

	if (strstr(nv_task_tab, "ctrl_task")) {
		mmi_init_idle_control();
	}

	if (strstr(nv_task_tab, "wifi_task")) {
		mmi_wifi_init();
	}

	if (strstr(nv_task_tab, "traffic_task")) {
		mmi_traffic_init();
	}

	if (strstr(nv_task_tab, "key_task")) {
		mmi_keyStrokes_init();
	}


	//��ʼ������led��ص�ҵ�� �������ʵ��
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
	}
	
#ifndef DISABLE_LCD
	//��ʼ������lcd��ص�ҵ�������ʵ��
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		if (strstr(nv_task_tab, "tip_task")) {
			mmi_init_lcd_tip();
		}
	}
#endif
	return 0;
}

/**********************************************************************************
��������:mmi ��ʾģʽ��ʼ��
***********************************************************************************/
static int mmi_showmode_init()
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_showmode_init \n");
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		mmi_initLed();
	}
	
#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_FIRST);
		mmi_initLcd(FALSE);
	}
#endif
	return 0;
}

/**********************************************************************************
��������:MMI��ں���  ������Ϣ���� �����Ƴ�ʼ�� ������Ϣ
***********************************************************************************/
VOID mmi_powerOnLcdEntry()
{
	pthread_t mmi_handlemsg_thread;
	//����MMI��Ϣ����
	if ((g_mmi_msgQue_Id = mmi_create_msg_queue()) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI create g_mmi_msgQue_Id failed!!!\n");
		MMI_ASSERT(0);
	}
	pthread_mutex_init(&g_mmi_lcdmode_mutex, NULL);
	pthread_mutex_init(&g_mmi_ledmode_mutex, NULL);
	pthread_mutex_init(&g_mmi_update_flag_mutex, NULL);
	pthread_mutex_init(&g_mmi_refresh_lcd_mutex, NULL);
	sem_init(&g_mmi_update_sem_id, 0, 0);
	sem_init(&g_mmi_init_sem_id, 0, 0);
	sem_init(&g_mmi_traffic_warn_sem_id, 0, 0);
	sem_init(&g_mmi_poweroff_sem_id, 0, 0); //���ڹػ�ʱ��֤LCD������ػ���������ƽ��LED�ƶ���֮�������

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI start set_mmi_active\n");
	set_wake_lock(MMI_MAIN_LOCK_ID);

	//����MMI ������Ϣ�߳�
	if (pthread_create(&mmi_handlemsg_thread, NULL, &mmi_handleMsgEntry, NULL) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI pthread_create handle msg error\n");
		return ;
	}
	mmi_showmode_init();

	mmi_task_register();
	mmi_task_init();

	mmi_set_poweron_show();
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI main Register finish!!\n");

	//����MMI�����߳�
	mmi_mainControllerEntry(NULL);
}

/**********************************************************************************
��������:�ػ����
***********************************************************************************/
VOID mmi_poweroffcharger_init()
{

	UINT32 i = 0;
	pthread_t mmi_handlemsg_thread;
	//����MMI��Ϣ����
	if ((g_mmi_msgQue_Id = mmi_create_msg_queue()) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI create g_mmi_msgQue_Id failed!!!\n");
		MMI_ASSERT(0);
	}

	pthread_t mmi_offchg_mainctrl_thread;
	pthread_mutex_init(&g_mmi_update_flag_mutex, NULL);
	pthread_mutex_init(&g_mmi_poweron_mutex, NULL);
	sem_init(&g_mmi_update_sem_id, 0, 0);
	sem_init(&g_mmi_init_sem_id, 0, 0);
	
#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		sem_init(&g_mmi_gui_init_sem_id, 0, 0);
	}
#endif

	//����MMI ������Ϣ�߳�
	if (pthread_create(&mmi_handlemsg_thread, NULL, &mmi_handleMsgEntry, NULL) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI pthread_create handle msg error\n");
		return ;
	}

	for (i = 0; i < sizeof(g_zMMITaskInfoTab) / sizeof(T_zMMITaskInfoItem); ++ i) {
		g_zMMITaskInfoTab[i].task = MMI_TASK_INVALID;
	}

	offchg_RegisterPocTaskInfoItem();
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		mmi_initLed();
	}
#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_initLcd(TRUE);
	}
#endif

	int res = pthread_create(&mmi_offchg_mainctrl_thread, NULL, &mmi_mainControllerEntry, NULL);
	if (res != 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI pthread_create main control error\n");
		assert(0);
	}
}

static int get_nv()
{
	char mmi_nv_value[NV_CONTENT_LEN] = {0};
	SINT32 ret_fd = -1;
	int state = 0;

	//����mmi��ʾģʽȫ�ֱ���
	cfg_get_item("mmi_showmode", mmi_nv_value, sizeof(mmi_nv_value));
	if (0 == strcmp(mmi_nv_value, "led")) {
		g_showMode = MMI_MODE_LED;
		ret_fd = open(LCD_PATH, O_RDWR);
		if (ret_fd == -1) 
		{
			slog(MMI_PRINT, SLOG_ERR,"ZTE_MMI mmi_file_operate open file fail: %s!\n", LCD_PATH);
		}
		else
		{
			if(ioctl(ret_fd, LEADT15DS26_SET_BACKLIGHT, &state) < 0)
			{
				slog(MMI_PRINT, SLOG_ERR,"get_nv ioctl fail!\n");
			}
		}
	} else if (0 == strcmp(mmi_nv_value, "lcd")) {
		g_showMode = MMI_MODE_LCD;
	} else if (0 == strcmp(mmi_nv_value, "all")) {
		g_showMode = MMI_MODE_ALL;
	} else {
		assert(0);
	}
	
	cfg_get_item("customer_type",mmi_nv_value,sizeof(mmi_nv_value));
	if(0 == strcmp(mmi_nv_value, "sdk_min"))
	{
		g_customer_type = CUSTOMER_SDK_MIN;
	}
	else if(0 == strcmp(mmi_nv_value, "guodian"))
	{
		g_customer_type = CUSTOMER_GUODIAN;
	}
	else if(0 == strcmp(mmi_nv_value, "nandian"))
	{
		g_customer_type = CUSTOMER_NANDIAN;
	}

	cfg_get_item("mmi_use_protect", mmi_nv_value, sizeof(mmi_nv_value));

	if (strstr(mmi_nv_value, "discharge_protect")) {
		g_discharge_protect = 1;
	}

	if (strstr(mmi_nv_value, "temp_protect")) {
		g_temp_protect = 1;
		
		if (strstr(mmi_nv_value, "charge_protect")) {
			g_charge_protect = 1;
		}
	}

	cfg_get_item("mmi_fast_poweron", mmi_nv_value, sizeof(mmi_nv_value));

	if (0 == strcmp(mmi_nv_value, "1")) {
		g_fast_poweron = 1;

	}

	cfg_get_item("mmi_led_mode", mmi_nv_value, sizeof(mmi_nv_value));

	if (strstr(mmi_nv_value, "standby_mode")) {
		g_led_standby_mode = 1;

	}

	if (strstr(mmi_nv_value, "sleep_mode")) {
		g_led_sleep_mode = 1;
	}


	cfg_get_item("mmi_use_wifi_usernum", mmi_nv_value, sizeof(mmi_nv_value));

	if (0 == strcmp(mmi_nv_value, "1")) {
		g_use_wifi_usernum = 1;
	}

	cfg_get_item("mmi_show_pagetab", mmi_nv_value, sizeof(mmi_nv_value));

	if (strstr(mmi_nv_value, "page1")) {
		g_show_pagefirst = 1;
	}
	if (strstr(mmi_nv_value, "page2")) {
		g_show_pagesecond = 1;
	}
	if (strstr(mmi_nv_value, "page3")) {
		g_show_pagethird = 1;
	}
	/*
	cfg_get_item("mmi_temp_voltage_line", mmi_nv_value, sizeof(mmi_nv_value));
	{
		char *tmp = NULL;
		char *substr = strtok_r(mmi_nv_value, "+",&tmp);
		int temp[4] = {0};
		int i = 0;
		while (substr != NULL) {
			temp[i] = atoi(substr);
			i++;
			substr = strtok_r(NULL, "+",&tmp);
		}
		g_mmi_superhightempvol = temp[0];
		g_mmi_hightempvol = temp[1];
		g_mmi_lowtempvol = temp[2];
		g_mmi_superlowtempvol = temp[3];
	}
	*/
	cfg_get_item("mmi_battery_voltage_line", mmi_nv_value, sizeof(mmi_nv_value));
	{
		char *tmp = NULL;
		char *substr = strtok_r(mmi_nv_value, "+",&tmp);
		int i = 0;
		while (substr != NULL) {
			g_mmi_batvoltageline[i] = atoi(substr);
			slog(MMI_PRINT, SLOG_DEBUG, "g_mmi_batvoltageline[%d] = %d\n", i, g_mmi_batvoltageline[i]);
			i++;
			substr = strtok_r(NULL, "+",&tmp);
		}
	}

#if defined(QRZL_UE) && (defined(JCV_HW_MZ901_V1_0)  ||  defined(JCV_HW_GS28V_V1))
        cfg_get_item("mmi_charging_voltage_line", mmi_nv_value, sizeof(mmi_nv_value));
        {
                char *tmp = NULL;
                char *substr = strtok_r(mmi_nv_value, "+",&tmp);
                int i = 0;
                while (substr != NULL) {
                        g_mmi_chgvoltageline[i] = atoi(substr);
                        slog(MMI_PRINT, SLOG_DEBUG, "g_mmi_chgvoltageline[%d] = %d\n", i, g_mmi_chgvoltageline[i]);
                        i++;
                        substr = strtok_r(NULL, "+",&tmp);
                }
        }
#endif

	if(ret_fd >= 0)
	close(ret_fd);
	return 0;
}

/**********************************************************************************
��������:MMI��ں���  ������Ϣ���� �����Ƴ�ʼ�� ������Ϣ
***********************************************************************************/
int zte_mmi_main(int argc, char * argv[])
{
	prctl(PR_SET_NAME, "mmi", 0, 0, 0);
	//����NV��ʼ����ӡ���𣬲�ע�ᶯ̬������ӡ�����ź���
	loglevel_init();
	
	//zdm��nv����ת����ȫ�ֱ������й��ܿ���
	get_nv();
#ifndef QRZL_UE
	mmi_BatLedOffOpt();//��boot��ĵ�
#endif
#ifdef DISABLE_LCD
	if (g_showMode != MMI_MODE_LED) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI MIN only support LED \n");
		return -1;
	}
#endif

	if (argc >= 2) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI ###POWER OFF CHARGER START####\n");
		g_mmi_power_mode = 0;
#ifdef QRZL_UE
		mmi_OffKernelLedOpt(1);
#endif
		zMmi_PowerOffChargerEntry();
	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI ######START######\n");
		g_mmi_power_mode = 1;
#ifdef QRZL_UE
		mmi_OffKernelLedOpt(0);
#endif
		mmi_powerOnLcdEntry();
	}
	return 0;
}

